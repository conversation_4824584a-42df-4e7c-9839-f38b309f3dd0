/**
 * WebGL上下文丢失测试工具
 * 用于测试和验证WebGL上下文丢失修复效果
 */

export class WebGLTester {
  private canvas: HTMLCanvasElement | null = null;
  private gl: WebGLRenderingContext | null = null;
  private extension: WEBGL_lose_context | null = null;

  constructor() {
    this.findCanvas();
  }

  /**
   * 查找页面中的Canvas元素
   */
  private findCanvas(): void {
    // 查找主渲染Canvas
    const canvases = document.querySelectorAll('canvas');
    for (const canvas of canvases) {
      if (canvas.width > 100 && canvas.height > 100) {
        this.canvas = canvas;
        this.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (this.gl) {
          this.extension = this.gl.getExtension('WEBGL_lose_context');
          break;
        }
      }
    }
  }

  /**
   * 手动触发WebGL上下文丢失
   */
  public loseContext(): boolean {
    if (!this.extension) {
      console.error('WEBGL_lose_context扩展不可用');
      return false;
    }

    console.log('手动触发WebGL上下文丢失...');
    this.extension.loseContext();
    return true;
  }

  /**
   * 手动恢复WebGL上下文
   */
  public restoreContext(): boolean {
    if (!this.extension) {
      console.error('WEBGL_lose_context扩展不可用');
      return false;
    }

    console.log('手动恢复WebGL上下文...');
    this.extension.restoreContext();
    return true;
  }

  /**
   * 检查WebGL上下文状态
   */
  public checkContextStatus(): string {
    if (!this.gl) {
      return 'WebGL上下文不可用';
    }

    const status = this.gl.isContextLost();
    return status ? 'WebGL上下文已丢失' : 'WebGL上下文正常';
  }

  /**
   * 获取WebGL信息
   */
  public getWebGLInfo(): any {
    if (!this.gl) {
      return { error: 'WebGL上下文不可用' };
    }

    return {
      vendor: this.gl.getParameter(this.gl.VENDOR),
      renderer: this.gl.getParameter(this.gl.RENDERER),
      version: this.gl.getParameter(this.gl.VERSION),
      shadingLanguageVersion: this.gl.getParameter(this.gl.SHADING_LANGUAGE_VERSION),
      maxTextureSize: this.gl.getParameter(this.gl.MAX_TEXTURE_SIZE),
      maxVertexAttribs: this.gl.getParameter(this.gl.MAX_VERTEX_ATTRIBS),
      maxVaryingVectors: this.gl.getParameter(this.gl.MAX_VARYING_VECTORS),
      maxFragmentUniforms: this.gl.getParameter(this.gl.MAX_FRAGMENT_UNIFORM_VECTORS),
      maxVertexUniforms: this.gl.getParameter(this.gl.MAX_VERTEX_UNIFORM_VECTORS),
      contextLost: this.gl.isContextLost()
    };
  }

  /**
   * 压力测试 - 创建大量WebGL资源
   */
  public stressTest(): void {
    if (!this.gl) {
      console.error('WebGL上下文不可用');
      return;
    }

    console.log('开始WebGL压力测试...');
    
    // 创建大量纹理
    const textures: WebGLTexture[] = [];
    for (let i = 0; i < 100; i++) {
      const texture = this.gl.createTexture();
      if (texture) {
        this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
        this.gl.texImage2D(
          this.gl.TEXTURE_2D, 0, this.gl.RGBA, 512, 512, 0,
          this.gl.RGBA, this.gl.UNSIGNED_BYTE, null
        );
        textures.push(texture);
      }
    }

    // 创建大量缓冲区
    const buffers: WebGLBuffer[] = [];
    for (let i = 0; i < 100; i++) {
      const buffer = this.gl.createBuffer();
      if (buffer) {
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, buffer);
        this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(10000), this.gl.STATIC_DRAW);
        buffers.push(buffer);
      }
    }

    console.log(`创建了 ${textures.length} 个纹理和 ${buffers.length} 个缓冲区`);
    
    // 清理资源
    setTimeout(() => {
      textures.forEach(texture => this.gl?.deleteTexture(texture));
      buffers.forEach(buffer => this.gl?.deleteBuffer(buffer));
      console.log('压力测试资源已清理');
    }, 5000);
  }
}

// 全局测试实例
let webglTester: WebGLTester | null = null;

/**
 * 获取WebGL测试器实例
 */
export function getWebGLTester(): WebGLTester {
  if (!webglTester) {
    webglTester = new WebGLTester();
  }
  return webglTester;
}

/**
 * 在控制台中暴露测试函数
 */
export function exposeWebGLTester(): void {
  if (typeof window !== 'undefined') {
    const tester = getWebGLTester();
    
    // @ts-ignore
    window.webglTest = {
      loseContext: () => tester.loseContext(),
      restoreContext: () => tester.restoreContext(),
      checkStatus: () => {
        const status = tester.checkContextStatus();
        console.log(status);
        return status;
      },
      getInfo: () => {
        const info = tester.getWebGLInfo();
        console.table(info);
        return info;
      },
      stressTest: () => tester.stressTest(),
      help: () => {
        console.log(`
WebGL测试工具使用说明：
- webglTest.loseContext()     手动触发上下文丢失
- webglTest.restoreContext()  手动恢复上下文
- webglTest.checkStatus()     检查上下文状态
- webglTest.getInfo()         获取WebGL信息
- webglTest.stressTest()      进行压力测试
- webglTest.help()            显示帮助信息
        `);
      }
    };
    
    console.log('WebGL测试工具已加载，输入 webglTest.help() 查看使用说明');
  }
}
