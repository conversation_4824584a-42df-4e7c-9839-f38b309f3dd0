import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import './file-upload.css';

import { Icon } from '@iconify/react';

interface FileUploadProps {
  onUpload: (file: File) => void;
  className?: string;
  accept?: string[];
  title?: string;
  subtitle?: string;
  initialPreviewUrl?: string | null;
  isUploading?: boolean;
  progress?: number;
  uploadMessage?: string;
  previewType?: 'image' | 'none';
  initialFileName?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({ 
  onUpload, 
  className = '', 
  accept = ['image/png', 'image/jpeg'],
  title = '点击或拖拽文件到此区域',
  subtitle = '支持 PNG, JPG, JPEG 格式',
  initialPreviewUrl,
  isUploading = false,
  progress = 0,
  uploadMessage = '',
  previewType = 'image',
  initialFileName = ''
}) => {
  const [preview, setPreview] = useState<string | null>(initialPreviewUrl || null);
  const [fileName, setFileName] = useState<string>(initialFileName);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      onUpload(file);
      setFileName(file.name);

      if (previewType === 'image' && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setPreview(null);
      }
    }
  }, [onUpload, previewType]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: accept.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    multiple: false,
  });

  return (
    <div
      {...getRootProps()}
      className={`file-upload ${isDragActive ? 'active' : ''} ${className}`}>
      <input {...getInputProps()} disabled={isUploading} />
      {isUploading ? (
        <div className="file-upload-status">
          <p className="upload-message">{uploadMessage}</p>
          <div className="progress-bar-container">
            <div className="progress-bar" style={{ width: `${progress}%` }}></div>
          </div>
          <span className="upload-percentage">{`${Math.round(progress)}%`}</span>
        </div>
      ) : preview ? (
        <img src={preview} alt="Preview" className="file-upload-preview" />
      ) : fileName ? (
        <div className="file-upload-placeholder">
          <Icon icon="solar:file-check-bold" className="upload-icon" />
          <p className="title">已选择文件</p>
          <p className="subtitle">{fileName}</p>

        </div>
      ) : (
        <div className="file-upload-placeholder">
          <Icon icon="solar:upload-cloud-outline" className="upload-icon" />
          <p className="title">{title}</p>
          <p className="subtitle">{subtitle}</p>

        </div>
      )}
    </div>
  );
};
