import { useEffect, useRef, useState, useCallback } from 'react';
import * as THREE from 'three';

interface WebGLMemoryInfo {
  totalJSHeapSize?: number;
  usedJSHeapSize?: number;
  jsHeapSizeLimit?: number;
}

interface WebGLStats {
  geometries: number;
  textures: number;
  programs: number;
  calls: number;
  triangles: number;
  points: number;
  lines: number;
}

export const useWebGLMonitor = (renderer: THREE.WebGLRenderer | null) => {
  const [memoryInfo, setMemoryInfo] = useState<WebGLMemoryInfo>({});
  const [webglStats, setWebglStats] = useState<WebGLStats>({
    geometries: 0,
    textures: 0,
    programs: 0,
    calls: 0,
    triangles: 0,
    points: 0,
    lines: 0
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  const intervalRef = useRef<number | null>(null);

  // 获取内存信息
  const getMemoryInfo = useCallback((): WebGLMemoryInfo => {
    // @ts-ignore - performance.memory 是实验性API
    const memory = (performance as any).memory;
    if (memory) {
      return {
        totalJSHeapSize: memory.totalJSHeapSize,
        usedJSHeapSize: memory.usedJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }
    return {};
  }, []);

  // 获取WebGL统计信息
  const getWebGLStats = useCallback((): WebGLStats => {
    if (!renderer) {
      return {
        geometries: 0,
        textures: 0,
        programs: 0,
        calls: 0,
        triangles: 0,
        points: 0,
        lines: 0
      };
    }

    const info = renderer.info;
    return {
      geometries: info.memory.geometries,
      textures: info.memory.textures,
      programs: info.programs?.length || 0,
      calls: info.render.calls,
      triangles: info.render.triangles,
      points: info.render.points,
      lines: info.render.lines
    };
  }, [renderer]);

  // 检查内存使用是否过高
  const checkMemoryUsage = useCallback(() => {
    const memory = getMemoryInfo();
    const stats = getWebGLStats();
    
    setMemoryInfo(memory);
    setWebglStats(stats);

    // 检查内存使用警告阈值
    if (memory.usedJSHeapSize && memory.jsHeapSizeLimit) {
      const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      if (usageRatio > 0.8) {
        console.warn('内存使用率过高:', (usageRatio * 100).toFixed(1) + '%');
        return true;
      }
    }

    // 检查WebGL资源是否过多
    if (stats.geometries > 1000 || stats.textures > 500) {
      console.warn('WebGL资源过多:', stats);
      return true;
    }

    return false;
  }, [getMemoryInfo, getWebGLStats]);

  // 强制垃圾回收（如果可用）
  const forceGarbageCollection = useCallback(() => {
    // @ts-ignore - gc 是实验性API
    if (window.gc) {
      // @ts-ignore
      window.gc();
      console.log('已触发垃圾回收');
    }
  }, []);

  // 清理WebGL资源
  const cleanupWebGLResources = useCallback(() => {
    if (!renderer) return;

    // 清理未使用的程序
    renderer.info.programs?.forEach((program: any) => {
      if (program.usedTimes === 0) {
        renderer.dispose();
      }
    });

    console.log('WebGL资源清理完成');
  }, [renderer]);

  // 开始监控
  const startMonitoring = useCallback((interval: number = 5000) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    setIsMonitoring(true);
    intervalRef.current = window.setInterval(() => {
      const isHighUsage = checkMemoryUsage();
      
      if (isHighUsage) {
        // 尝试清理资源
        cleanupWebGLResources();
        forceGarbageCollection();
      }
    }, interval);
  }, [checkMemoryUsage, cleanupWebGLResources, forceGarbageCollection]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsMonitoring(false);
  }, []);

  // 清理
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  return {
    memoryInfo,
    webglStats,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    checkMemoryUsage,
    forceGarbageCollection,
    cleanupWebGLResources
  };
};
