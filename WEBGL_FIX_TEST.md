# WebGL上下文丢失修复验证指南

## 修复内容总结

本次修复解决了Three.js渲染器WebGL上下文丢失的问题，主要包括以下改进：

### 1. WebGL上下文丢失/恢复事件处理
- ✅ 添加了`webglcontextlost`和`webglcontextrestored`事件监听器
- ✅ 实现了优雅的上下文丢失处理和自动恢复机制
- ✅ 在上下文恢复后自动重新初始化渲染器

### 2. 纹理缓存清理机制
- ✅ 实现了全局纹理缓存的清理功能
- ✅ 在模型切换和组件卸载时正确释放纹理资源
- ✅ 防止纹理内存泄漏

### 3. useGLTF缓存管理优化
- ✅ 在模型切换时正确清理useGLTF缓存
- ✅ 避免内存泄漏和资源冲突
- ✅ 实现模型切换前的缓存清理机制

### 4. 增强模型切换时的资源清理
- ✅ 完善模型切换逻辑，彻底清理前一个模型的所有资源
- ✅ 添加深度场景清理功能
- ✅ 避免几何体、材质、纹理资源冲突

### 5. MaterialThumbnail组件资源管理优化
- ✅ 改进材质缩略图组件的WebGL渲染器管理
- ✅ 添加WebGL上下文事件监听
- ✅ 避免多个小Canvas实例导致的资源竞争

### 6. 内存监控和错误恢复机制
- ✅ 实现WebGL内存使用监控
- ✅ 添加自动错误恢复机制
- ✅ 当检测到上下文丢失时自动重新初始化渲染器
- ✅ 开发模式下显示实时内存监控信息

## 测试验证步骤

### 1. 基本功能测试

1. **启动应用**
   ```bash
   npm run dev
   ```

2. **检查开发模式监控**
   - 打开浏览器开发者工具
   - 在右上角应该看到WebGL监控面板（仅开发模式）
   - 监控面板显示：几何体数量、纹理数量、程序数量、调用次数、内存使用

3. **模型切换测试**
   - 在模型下拉菜单中切换不同模型
   - 观察控制台日志，应该看到：
     - "已清理模型缓存: [模型路径]"
     - "Model组件资源已清理"
   - 确认切换流畅，无错误提示

4. **材质切换测试**
   - 选择可编辑材质
   - 在"会通材料"和"自定义"标签间切换
   - 应用不同的预设材质
   - 调整自定义材质参数
   - 确认材质切换正常，无上下文丢失错误

### 2. WebGL上下文丢失模拟测试

1. **打开浏览器控制台**
   - 等待页面完全加载（约2秒后）
   - 输入 `webglTest.help()` 查看测试工具说明

2. **手动触发上下文丢失**
   ```javascript
   // 检查当前状态
   webglTest.checkStatus()
   
   // 触发上下文丢失
   webglTest.loseContext()
   
   // 检查状态（应显示"WebGL上下文已丢失"）
   webglTest.checkStatus()
   
   // 恢复上下文
   webglTest.restoreContext()
   
   // 再次检查状态（应显示"WebGL上下文正常"）
   webglTest.checkStatus()
   ```

3. **观察恢复过程**
   - 触发上下文丢失后，应该看到警告通知："3D渲染暂时中断，正在恢复..."
   - 恢复后应该看到成功通知："3D渲染已恢复"
   - 3D场景应该正常显示，无需手动刷新页面

### 3. 压力测试

1. **内存压力测试**
   ```javascript
   // 进行WebGL资源压力测试
   webglTest.stressTest()
   ```
   - 观察内存监控面板中的数值变化
   - 5秒后资源应该自动清理
   - 确认无内存泄漏

2. **长时间使用测试**
   - 连续切换模型和材质30次以上
   - 观察内存监控数值是否持续增长
   - 正常情况下内存使用应该保持稳定

### 4. 多标签页测试

1. **打开多个标签页**
   - 同时打开3-5个渲染页面标签
   - 在各个标签页中进行模型和材质切换
   - 确认所有标签页都能正常工作

2. **标签页切换测试**
   - 在标签页间快速切换
   - 确认WebGL上下文不会因为标签页切换而丢失

### 5. 错误恢复测试

1. **网络中断模拟**
   - 在开发者工具中模拟网络中断
   - 尝试切换模型
   - 恢复网络后确认功能正常

2. **GPU压力测试**
   - 同时运行其他GPU密集型应用
   - 观察WebGL上下文是否稳定
   - 如果发生上下文丢失，确认能自动恢复

## 预期结果

### ✅ 成功指标
- 模型切换流畅，无"Context Lost"错误
- 材质切换正常，无渲染异常
- 内存使用稳定，无持续增长
- 上下文丢失后能自动恢复
- 控制台无WebGL相关错误

### ❌ 失败指标
- 出现"THREE.WebGLRenderer: Context Lost"错误
- 模型切换后显示异常或回退
- 内存使用持续增长不释放
- 上下文丢失后无法恢复
- 页面需要刷新才能正常使用

## 故障排除

如果测试中发现问题：

1. **检查浏览器兼容性**
   - 确保使用支持WebGL的现代浏览器
   - 检查GPU驱动是否最新

2. **查看控制台日志**
   - 观察是否有未捕获的错误
   - 检查资源清理日志是否正常

3. **检查内存监控**
   - 确认内存监控功能正常工作
   - 观察资源数量变化是否合理

4. **验证事件监听器**
   - 确认WebGL上下文事件监听器已正确添加
   - 检查事件处理函数是否被调用

## 性能优化建议

1. **定期清理缓存**
   - 在长时间使用后手动清理缓存
   - 监控内存使用情况

2. **合理使用材质**
   - 避免同时应用过多复杂材质
   - 及时清理不需要的纹理

3. **监控资源使用**
   - 定期检查WebGL资源数量
   - 在开发模式下观察监控面板
